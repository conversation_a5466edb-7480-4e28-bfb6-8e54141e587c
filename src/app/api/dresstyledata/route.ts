import { mkdir } from "fs/promises";
import { NextRequest, NextResponse } from "next/server";
import path from "path";

export async function POST(req:NextRequest){
    const formData = await req.formData();
    
    const [title,imgpath,colSpan,colStart] = ["title","imgpath","colStart","colSpan"].map(items=>formData.getAll(items));
    // get the filename
    const fileName = imgpath.map(items=> items instanceof File? items.name : null);
    // renameFile
    const reformation = fileName.map((items)=>{
        const separate = items?.split(".");
        const imgName = separate?.[0];
        const renameImg = (imgName?imgName : "") + Date.now();
        const addFormat = renameImg + "." + separate?.[1];

        return addFormat;
    })
    // bufferfile
    const buffer = await Promise.all(
        imgpath.map(async (items)=>{
            return items instanceof File ? Buffer.from(await items.arrayBuffer()) : "no file"
        })
    );
    // place file to the public folder
    const uploadDir = path.join(process.cwd(),"public/assets");

    // const arr = title.map((items,index)=>({title:items,imgpath:reformation[index],colSpan:colSpan[index],colStart:colStart[index]}));

    try{
        await mkdir(uploadDir,)
    }
}

// ===========first-step===============
// image store with unique name
// for this we have to code something
// after store place it to array of object
// save it to database
